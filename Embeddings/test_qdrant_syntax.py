#!/usr/bin/env python3
"""
Test script to verify the syntax and basic functionality of the Qdrant script
without requiring actual Qdrant Cloud credentials.
"""

import os
import sys
import PyPDF2
import re
import numpy as np
from sentence_transformers import SentenceTransformer
from rank_bm25 import BM25Okapi

# Test PDF reading and chunking
def test_pdf_processing():
    print("Testing PDF processing...")
    
    pdf_path = "/Users/<USER>/SecuriGeek/Test Programs/Embeddings/Text to Sparse/sample/test_data.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"PDF file not found: {pdf_path}")
        return False
    
    # Read PDF
    pdf_text = []
    try:
        with open(pdf_path, "rb") as f:
            reader = PyPDF2.PdfReader(f)
            for page in reader.pages:
                pdf_text.append(page.extract_text())
        
        full_text = " ".join(pdf_text)
        print(f"✓ PDF read successfully. Total characters: {len(full_text)}")
        
        # Test chunking function
        def split_text_into_chunks(text, chunk_size=100):
            text = re.sub(r'\s+', ' ', text.strip())
            sentences = re.split(r'(?<=[.!?])\s+', text)
            
            chunks = []
            current_chunk = ""
            
            for sentence in sentences:
                sentence = sentence.strip()
                if not sentence:
                    continue
                    
                current_words = len(current_chunk.split()) if current_chunk else 0
                sentence_words = len(sentence.split())
                
                if current_words + sentence_words <= chunk_size:
                    current_chunk = current_chunk + " " + sentence if current_chunk else sentence
                else:
                    if current_chunk.strip():
                        chunks.append(current_chunk.strip())
                    current_chunk = sentence
            
            if current_chunk.strip():
                chunks.append(current_chunk.strip())
            
            return chunks
        
        chunks = split_text_into_chunks(full_text, chunk_size=100)
        print(f"✓ Text chunked successfully. Total chunks: {len(chunks)}")
        print(f"✓ Average chunk length: {np.mean([len(chunk.split()) for chunk in chunks]):.1f} words")
        
        return chunks
        
    except Exception as e:
        print(f"✗ Error processing PDF: {e}")
        return False

# Test embeddings generation
def test_embeddings(chunks):
    print("\nTesting embeddings generation...")
    
    try:
        # Test dense embeddings
        print("Loading sentence transformer model...")
        model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # Test with first few chunks to avoid long processing time
        test_chunks = chunks[:5] if len(chunks) > 5 else chunks
        
        dense_embeddings = model.encode(test_chunks).tolist()
        print(f"✓ Dense embeddings generated. Shape: {len(dense_embeddings)} x {len(dense_embeddings[0])}")
        
        # Test sparse embeddings
        tokenized_chunks = [chunk.lower().split() for chunk in test_chunks]
        bm25 = BM25Okapi(tokenized_chunks)
        
        # Create vocabulary
        vocab = set()
        for tokens in tokenized_chunks:
            vocab.update(tokens)
        vocab = sorted(list(vocab))
        vocab_to_idx = {word: idx for idx, word in enumerate(vocab)}
        
        print(f"✓ Vocabulary created. Size: {len(vocab)}")
        
        # Generate sparse embeddings
        sparse_embeddings = []
        for i, tokens in enumerate(tokenized_chunks):
            indices = []
            values = []
            
            for token in set(tokens):
                if token in vocab_to_idx:
                    idx = vocab_to_idx[token]
                    tf = tokens.count(token) / len(tokens)
                    indices.append(idx)
                    values.append(float(tf))
            
            sparse_embeddings.append({"indices": indices, "values": values})
        
        print(f"✓ Sparse embeddings generated. Count: {len(sparse_embeddings)}")
        
        return model, dense_embeddings, sparse_embeddings, vocab_to_idx
        
    except Exception as e:
        print(f"✗ Error generating embeddings: {e}")
        return False

# Test Qdrant data structures
def test_qdrant_structures(chunks, dense_embeddings, sparse_embeddings):
    print("\nTesting Qdrant data structures...")
    
    try:
        from qdrant_client.models import PointStruct, SparseVector
        
        # Test creating points
        points = []
        test_chunks = chunks[:5] if len(chunks) > 5 else chunks
        
        for i, chunk in enumerate(test_chunks):
            sparse_vector = SparseVector(
                indices=sparse_embeddings[i]["indices"],
                values=sparse_embeddings[i]["values"]
            )
            
            point = PointStruct(
                id=i,
                payload={
                    "text": chunk,
                    "chunk_id": i,
                    "word_count": len(chunk.split())
                },
                vector={
                    "dense": dense_embeddings[i],
                    "text-sparse": sparse_vector
                }
            )
            points.append(point)
        
        print(f"✓ PointStruct objects created successfully. Count: {len(points)}")
        print(f"✓ First point ID: {points[0].id}")
        print(f"✓ First point payload keys: {list(points[0].payload.keys())}")
        print(f"✓ Vector structure: {list(points[0].vector.keys())}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error creating Qdrant structures: {e}")
        return False

def main():
    print("=" * 60)
    print("QDRANT SCRIPT SYNTAX AND FUNCTIONALITY TEST")
    print("=" * 60)
    
    # Test PDF processing
    chunks = test_pdf_processing()
    if not chunks:
        print("✗ PDF processing failed. Exiting.")
        return False
    
    # Test embeddings
    embedding_results = test_embeddings(chunks)
    if not embedding_results:
        print("✗ Embeddings generation failed. Exiting.")
        return False
    
    model, dense_embeddings, sparse_embeddings, vocab_to_idx = embedding_results
    
    # Test Qdrant structures
    if not test_qdrant_structures(chunks, dense_embeddings, sparse_embeddings):
        print("✗ Qdrant structures test failed. Exiting.")
        return False
    
    print("\n" + "=" * 60)
    print("✓ ALL TESTS PASSED! The script syntax is correct.")
    print("✓ You can now run the main script with proper Qdrant credentials.")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
