from PyPDF2 import PdfReader
from transformers import AutoTokenizer, AutoModelForMaskedLM
import torch
import json
from scipy.sparse import csr_matrix, save_npz

# Step 1: Extract PDF text
def extract_pdf_text(pdf_path):
    reader = PdfReader(pdf_path)
    text_pages = []
    for page in reader.pages:
        text_pages.append(page.extract_text())
    return text_pages

pdf_path = "/Users/<USER>/SecuriGeek/Test Programs/Embeddings/Text to Sparse/sample/test_data.pdf"
pages = extract_pdf_text(pdf_path)

# Step 2: Load SPLADE model
tokenizer = AutoTokenizer.from_pretrained("naver/splade-cocondenser-ensembledistil")
model = AutoModelForMaskedLM.from_pretrained("naver/splade-cocondenser-ensembledistil")

def splade_encode(text):
    inputs = tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
    with torch.no_grad():
        outputs = model(**inputs).logits  # [batch, seq_len, vocab_size]
        sparse_vec = torch.log(1 + torch.relu(outputs)).max(dim=1).values.squeeze()
    return sparse_vec  # size = vocab_size

# Step 3: Convert to sparse dict (for saving as JSON)
def to_sparse_dict(sparse_tensor):
    nonzero = torch.nonzero(sparse_tensor).squeeze().tolist()
    if isinstance(nonzero, int):  # handle single index
        nonzero = [nonzero]
    values = sparse_tensor[nonzero].tolist()
    sparse_dict = {int(idx): float(val) for idx, val in zip(nonzero, values)}
    return sparse_dict

# Step 4: Generate + Save embeddings
page_embeddings = {}
rows, cols, data = [], [], []

for i, page in enumerate(pages):
    emb = splade_encode(page)
    sparse_dict = to_sparse_dict(emb)

    # Save in dictionary (for JSON)
    page_embeddings[f"page_{i+1}"] = sparse_dict

    # Prepare for sparse matrix
    nonzero = list(sparse_dict.keys())
    values = list(sparse_dict.values())
    rows.extend([i] * len(nonzero))
    cols.extend(nonzero)
    data.extend(values)

# Save as JSON
with open("splade_embeddings.json", "w") as f:
    json.dump(page_embeddings, f)

# Save as Sparse Matrix
sparse_matrix = csr_matrix((data, (rows, cols)), shape=(len(pages), emb.shape[0]))
save_npz("splade_embeddings.npz", sparse_matrix)

print("✅ Saved SPLADE embeddings:")
print(" - JSON file: splade_embeddings.json")
print(" - Sparse matrix: splade_embeddings.npz")
