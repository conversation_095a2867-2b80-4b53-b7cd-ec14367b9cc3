from PyPDF2 import Pdf<PERSON>ead<PERSON>
from rank_bm25 import BM25Okapi
import json

# Simple tokenizer (lowercase + split)
def simple_tokenize(text):
    return text.lower().split()

# Step 1: Extract text
def extract_pdf_text(pdf_path):
    reader = PdfReader(pdf_path)
    return [page.extract_text() for page in reader.pages]

pdf_path = "/Users/<USER>/SecuriGeek/Test Programs/Embeddings/Text to Sparse/sample/test_data.pdf"
pages = extract_pdf_text(pdf_path)

# Step 2: Tokenize
tokenized_corpus = [simple_tokenize(page) for page in pages]

# Step 3: BM25
bm25 = BM25Okapi(tokenized_corpus)

# Step 4: Query
query = "heart attack symptoms"
tokenized_query = simple_tokenize(query)
scores = bm25.get_scores(tokenized_query)

# Step 5: Collect results
results = []
for i, score in enumerate(scores):
    results.append({
        "page_number": i + 1,
        "score": float(score),
        "text_snippet": pages[i][:200]  # first 200 chars
    })

# Step 6: Save to JSON
output_file = "bm25_results.json"
with open(output_file, "w") as f:
    json.dump(results, f, indent=4)

print(f"Results saved to {output_file} ✅")
