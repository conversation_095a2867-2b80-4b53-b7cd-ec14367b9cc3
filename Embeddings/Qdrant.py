# pip install qdrant-client sentence-transformers rank_bm25 PyPDF2 python-dotenv

import os
from dotenv import load_dotenv
import PyPDF2
import re
import numpy as np
from qdrant_client import QdrantClient
from qdrant_client.models import (
    VectorParams, SparseVectorParams, Distance, PointStruct,
    NamedVector, NamedSparseVector, SparseVector
)
from sentence_transformers import SentenceTransformer
from rank_bm25 import BM25Okapi

# -------------------------------
# 0. Load environment variables
# -------------------------------
# .env example:
# QDRANT_URL=https://<your-cluster>.qdrant.io
# QDRANT_API_KEY=<your-api-key>
load_dotenv()
QDRANT_URL = os.getenv("QDRANT_URL")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")
if not QDRANT_URL or not QDRANT_API_KEY:
    raise ValueError("Please set QDRANT_URL and QDRANT_API_KEY in your .env file")

# -------------------------------
# 1. Read PDF and extract text
# -------------------------------
pdf_path = "/Users/<USER>/SecuriGeek/Test Programs/Embeddings/Text to Sparse/sample/test_data.pdf"  # Replace with your PDF path
pdf_text = []

with open(pdf_path, "rb") as f:
    reader = PyPDF2.PdfReader(f)
    for page in reader.pages:
        pdf_text.append(page.extract_text())

full_text = " ".join(pdf_text)

# -------------------------------
# 2. Split text into ~100-word chunks (no NLTK)
# -------------------------------
def split_text_into_chunks(text, chunk_size=100):
    """Split text into chunks of approximately chunk_size words using regex."""
    # Clean and normalize text
    text = re.sub(r'\s+', ' ', text.strip())

    # Split into sentences using regex
    sentences = re.split(r'(?<=[.!?])\s+', text)

    chunks = []
    current_chunk = ""

    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue

        # Check if adding this sentence would exceed chunk size
        current_words = len(current_chunk.split()) if current_chunk else 0
        sentence_words = len(sentence.split())

        if current_words + sentence_words <= chunk_size:
            current_chunk = current_chunk + " " + sentence if current_chunk else sentence
        else:
            # Save current chunk if it has content
            if current_chunk.strip():
                chunks.append(current_chunk.strip())
            current_chunk = sentence

    # Add the last chunk if it has content
    if current_chunk.strip():
        chunks.append(current_chunk.strip())

    return chunks

chunks = split_text_into_chunks(full_text, chunk_size=100)
print(f"Total chunks created: {len(chunks)}")
print(f"Average chunk length: {np.mean([len(chunk.split()) for chunk in chunks]):.1f} words")

# -------------------------------
# 3. Connect to Qdrant Cloud and setup collection
# -------------------------------
print("Connecting to Qdrant Cloud...")
client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY)

# Collection configuration
collection_name = "pdf_embeddings"
dense_dim = 384  # MiniLM embedding dimension

# Create collection if it doesn't exist
if not client.collection_exists(collection_name):
    print(f"Creating collection '{collection_name}'...")
    client.create_collection(
        collection_name=collection_name,
        vectors_config=VectorParams(size=dense_dim, distance=Distance.COSINE),
        sparse_vectors_config={"text-sparse": SparseVectorParams()}
    )
    print("Collection created successfully!")
else:
    print(f"Collection '{collection_name}' already exists.")

# -------------------------------
# 4. Generate dense and sparse embeddings
# -------------------------------
print("Loading sentence transformer model...")
model = SentenceTransformer('all-MiniLM-L6-v2')

print("Generating dense embeddings...")
dense_embeddings = model.encode(chunks, show_progress_bar=True).tolist()

print("Generating sparse embeddings with BM25...")
# Tokenize chunks for BM25
tokenized_chunks = [chunk.lower().split() for chunk in chunks]
bm25 = BM25Okapi(tokenized_chunks)

# Create vocabulary mapping for consistent sparse vector indices
vocab = set()
for tokens in tokenized_chunks:
    vocab.update(tokens)
vocab = sorted(list(vocab))
vocab_to_idx = {word: idx for idx, word in enumerate(vocab)}

print(f"Vocabulary size: {len(vocab)}")

# Generate sparse embeddings
sparse_embeddings = []
for i, tokens in enumerate(tokenized_chunks):
    # Get BM25 scores for this document against itself (self-similarity)
    scores = bm25.get_scores(tokens)

    # Create sparse vector using vocabulary indices
    indices = []
    values = []

    for token in set(tokens):  # Use unique tokens only
        if token in vocab_to_idx:
            idx = vocab_to_idx[token]
            # Use TF (term frequency) as the sparse value
            tf = tokens.count(token) / len(tokens)
            indices.append(idx)
            values.append(float(tf))

    sparse_embeddings.append({"indices": indices, "values": values})

# -------------------------------
# 5. Upload chunks with embeddings to Qdrant
# -------------------------------
print("Uploading chunks to Qdrant...")
points = []
for i, chunk in enumerate(chunks):
    # Create sparse vector using SparseVector wrapper
    sparse_vector = SparseVector(
        indices=sparse_embeddings[i]["indices"],
        values=sparse_embeddings[i]["values"]
    )

    points.append(
        PointStruct(
            id=i,
            payload={
                "text": chunk,
                "chunk_id": i,
                "word_count": len(chunk.split())
            },
            vector={
                "dense": dense_embeddings[i],
                "text-sparse": sparse_vector
            }
        )
    )

# Upload in batches for better performance
batch_size = 100
for i in range(0, len(points), batch_size):
    batch = points[i:i + batch_size]
    client.upsert(collection_name=collection_name, points=batch)
    print(f"Uploaded batch {i//batch_size + 1}/{(len(points) + batch_size - 1)//batch_size}")

print(f"Successfully uploaded {len(points)} chunks to Qdrant!")

# -------------------------------
# 6. Hybrid search function
# -------------------------------
def hybrid_search(query_text, top_k=5, dense_weight=0.7, sparse_weight=0.3):
    """
    Perform hybrid search combining dense and sparse embeddings.

    Args:
        query_text (str): The search query
        top_k (int): Number of results to return
        dense_weight (float): Weight for dense search results
        sparse_weight (float): Weight for sparse search results

    Returns:
        list: Top-k search results with combined scores
    """
    print(f"\nPerforming hybrid search for: '{query_text}'")

    # Generate dense query embedding
    dense_query = model.encode([query_text])[0].tolist()

    # Generate sparse query embedding
    tokenized_query = query_text.lower().split()
    query_indices = []
    query_values = []

    for token in set(tokenized_query):  # Use unique tokens only
        if token in vocab_to_idx:
            idx = vocab_to_idx[token]
            # Use TF (term frequency) as the sparse value
            tf = tokenized_query.count(token) / len(tokenized_query)
            query_indices.append(idx)
            query_values.append(float(tf))

    sparse_query = SparseVector(indices=query_indices, values=query_values)

    # Perform dense search
    dense_results = client.search(
        collection_name=collection_name,
        query_vector=("dense", dense_query),
        limit=top_k * 2,  # Get more results for reranking
        with_payload=True,
        with_vectors=False
    )

    # Perform sparse search
    sparse_results = client.search(
        collection_name=collection_name,
        query_vector=("text-sparse", sparse_query),
        limit=top_k * 2,  # Get more results for reranking
        with_payload=True,
        with_vectors=False
    )

    # Combine results with weighted scores
    combined_scores = {}

    # Add dense scores
    for result in dense_results:
        point_id = result.id
        combined_scores[point_id] = {
            'dense_score': result.score * dense_weight,
            'sparse_score': 0.0,
            'payload': result.payload
        }

    # Add sparse scores
    for result in sparse_results:
        point_id = result.id
        if point_id in combined_scores:
            combined_scores[point_id]['sparse_score'] = result.score * sparse_weight
        else:
            combined_scores[point_id] = {
                'dense_score': 0.0,
                'sparse_score': result.score * sparse_weight,
                'payload': result.payload
            }

    # Calculate final combined scores and sort
    final_results = []
    for point_id, scores in combined_scores.items():
        combined_score = scores['dense_score'] + scores['sparse_score']
        final_results.append({
            'id': point_id,
            'score': combined_score,
            'dense_score': scores['dense_score'],
            'sparse_score': scores['sparse_score'],
            'payload': scores['payload']
        })

    # Sort by combined score and return top-k
    final_results.sort(key=lambda x: x['score'], reverse=True)
    return final_results[:top_k]

# -------------------------------
# 7. Example search and results
# -------------------------------
# Example query - modify this to test with your PDF content
query = "machine learning algorithms"

# Perform hybrid search
search_results = hybrid_search(query, top_k=5)

print(f"\n{'='*80}")
print(f"TOP 5 RELEVANT CHUNKS FOR QUERY: '{query}'")
print(f"{'='*80}")

for i, result in enumerate(search_results, 1):
    print(f"\n--- RESULT {i} ---")
    print(f"Combined Score: {result['score']:.4f}")
    print(f"Dense Score: {result['dense_score']:.4f} | Sparse Score: {result['sparse_score']:.4f}")
    print(f"Chunk ID: {result['id']}")
    print(f"Word Count: {result['payload']['word_count']}")
    print(f"Text (first 200 chars): {result['payload']['text'][:200]}...")
    if len(result['payload']['text']) > 200:
        print(f"[Text continues for {len(result['payload']['text']) - 200} more characters]")

print(f"\n{'='*80}")
print("Search completed successfully!")
print(f"{'='*80}")
